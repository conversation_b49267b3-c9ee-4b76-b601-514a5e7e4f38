// 我的世界游戏主文件
class MinecraftGame {
    constructor() {
        this.scene = null;
        this.camera = null;
        this.renderer = null;
        this.world = new Map();
        this.player = null;
        this.controls = null;
        this.raycaster = new THREE.Raycaster();
        this.mouse = new THREE.Vector2();
        this.selectedBlock = 'grass';
        this.isPointerLocked = false;
        
        // 游戏设置
        this.CHUNK_SIZE = 16;
        this.WORLD_HEIGHT = 64;
        this.RENDER_DISTANCE = 4;
        
        // 方块类型定义
        this.blockTypes = {
            grass: { color: 0x7CB342, name: '草方块' },
            dirt: { color: 0x8D6E63, name: '泥土' },
            stone: { color: 0x757575, name: '石头' },
            wood: { color: 0x8D6E63, name: '木头' },
            leaves: { color: 0x4CAF50, name: '树叶' },
            sand: { color: 0xFDD835, name: '沙子' },
            water: { color: 0x2196F3, name: '水' },
            cobblestone: { color: 0x616161, name: '圆石' }
        };
        
        this.init();
    }
    
    init() {
        try {
            console.log('开始设置渲染器...');
            this.setupRenderer();

            console.log('开始设置场景...');
            this.setupScene();

            console.log('开始设置相机...');
            this.setupCamera();

            console.log('开始设置光照...');
            this.setupLights();

            console.log('开始设置控制器...');
            this.setupControls();

            console.log('开始设置事件监听器...');
            this.setupEventListeners();

            console.log('开始生成世界...');
            this.generateWorld();

            console.log('开始动画循环...');
            this.animate();

            console.log('游戏初始化完成！');

            // 隐藏加载界面
            document.getElementById('loading').classList.add('hidden');
        } catch (error) {
            console.error('游戏初始化过程中出错:', error);
            document.getElementById('loading').textContent = '初始化失败：' + error.message;
        }
    }
    
    setupRenderer() {
        const canvas = document.getElementById('canvas');
        this.renderer = new THREE.WebGLRenderer({ 
            canvas: canvas,
            antialias: true 
        });
        this.renderer.setSize(window.innerWidth, window.innerHeight);
        this.renderer.setClearColor(0x87CEEB); // 天空蓝色
        this.renderer.shadowMap.enabled = true;
        this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
    }
    
    setupScene() {
        this.scene = new THREE.Scene();
        this.scene.fog = new THREE.Fog(0x87CEEB, 50, 200);
    }
    
    setupCamera() {
        this.camera = new THREE.PerspectiveCamera(
            75, 
            window.innerWidth / window.innerHeight, 
            0.1, 
            1000
        );
        this.camera.position.set(0, 32, 0);
    }
    
    setupLights() {
        // 环境光
        const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
        this.scene.add(ambientLight);
        
        // 方向光（太阳光）
        const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
        directionalLight.position.set(50, 100, 50);
        directionalLight.castShadow = true;
        directionalLight.shadow.mapSize.width = 2048;
        directionalLight.shadow.mapSize.height = 2048;
        directionalLight.shadow.camera.near = 0.5;
        directionalLight.shadow.camera.far = 500;
        directionalLight.shadow.camera.left = -100;
        directionalLight.shadow.camera.right = 100;
        directionalLight.shadow.camera.top = 100;
        directionalLight.shadow.camera.bottom = -100;
        this.scene.add(directionalLight);
    }
    
    setupControls() {
        this.controls = new FirstPersonControls(this.camera, this.renderer.domElement);
    }
    
    setupEventListeners() {
        // 窗口大小调整
        window.addEventListener('resize', () => this.onWindowResize());
        
        // 鼠标事件
        this.renderer.domElement.addEventListener('click', () => this.requestPointerLock());
        this.renderer.domElement.addEventListener('mousedown', (event) => this.onMouseDown(event));
        this.renderer.domElement.addEventListener('mousemove', (event) => this.onMouseMove(event));
        
        // 键盘事件
        document.addEventListener('keydown', (event) => this.onKeyDown(event));

        // 右键菜单禁用
        this.renderer.domElement.addEventListener('contextmenu', (event) => {
            event.preventDefault();
        });
        
        // 指针锁定事件
        document.addEventListener('pointerlockchange', () => this.onPointerLockChange());
        
        // 物品栏点击
        document.querySelectorAll('.inventory-slot').forEach((slot, index) => {
            slot.addEventListener('click', () => this.selectInventorySlot(index));
        });
    }
    
    requestPointerLock() {
        this.renderer.domElement.requestPointerLock();
    }
    
    onPointerLockChange() {
        this.isPointerLocked = document.pointerLockElement === this.renderer.domElement;
        if (this.controls) {
            this.controls.enabled = this.isPointerLocked;
        }
    }
    
    onWindowResize() {
        this.camera.aspect = window.innerWidth / window.innerHeight;
        this.camera.updateProjectionMatrix();
        this.renderer.setSize(window.innerWidth, window.innerHeight);
    }
    
    onMouseDown(event) {
        if (!this.isPointerLocked) return;
        
        if (event.button === 0) { // 左键 - 破坏方块
            this.destroyBlock();
        } else if (event.button === 2) { // 右键 - 放置方块
            this.placeBlock();
        }
    }
    
    onMouseMove(event) {
        if (!this.isPointerLocked) return;
        
        this.mouse.x = 0;
        this.mouse.y = 0;
    }
    
    onKeyDown(event) {
        switch(event.code) {
            case 'Escape':
                document.exitPointerLock();
                break;
            case 'KeyF':
                this.toggleFullscreen();
                break;
            case 'KeyR':
                this.resetWorld();
                break;
            case 'KeyP':
                this.saveWorld();
                break;
            case 'KeyL':
                this.loadWorld();
                break;
            case 'Digit1':
            case 'Digit2':
            case 'Digit3':
            case 'Digit4':
            case 'Digit5':
            case 'Digit6':
            case 'Digit7':
            case 'Digit8':
                const slotIndex = parseInt(event.code.replace('Digit', '')) - 1;
                this.selectInventorySlot(slotIndex);
                break;
        }
    }
    
    selectInventorySlot(index) {
        const slots = document.querySelectorAll('.inventory-slot');
        if (index >= 0 && index < slots.length) {
            // 移除所有活动状态
            slots.forEach(slot => slot.classList.remove('active'));
            // 设置新的活动状态
            slots[index].classList.add('active');
            // 更新选中的方块类型
            this.selectedBlock = slots[index].dataset.block;
        }
    }
    
    generateWorld() {
        console.log('生成世界...');

        // 生成一个简单的平坦世界作为开始
        const worldSize = 32;
        const groundLevel = 30;

        for (let x = -worldSize; x < worldSize; x++) {
            for (let z = -worldSize; z < worldSize; z++) {
                // 生成地面层
                for (let y = 0; y < groundLevel; y++) {
                    let blockType = 'stone';
                    if (y === groundLevel - 1) blockType = 'grass';
                    else if (y > groundLevel - 4) blockType = 'dirt';

                    this.addBlock(x, y, z, blockType);
                }

                // 随机生成一些树
                if (Math.random() < 0.02 && this.getBlock(x, groundLevel, z)) {
                    this.generateTree(x, groundLevel, z);
                }
            }
        }
    }

    addBlock(x, y, z, type) {
        const key = `${x},${y},${z}`;
        if (this.world.has(key)) return;

        const geometry = new THREE.BoxGeometry(1, 1, 1);
        const material = new THREE.MeshLambertMaterial({
            color: this.blockTypes[type].color
        });
        const mesh = new THREE.Mesh(geometry, material);

        mesh.position.set(x, y, z);
        mesh.castShadow = true;
        mesh.receiveShadow = true;
        mesh.userData = { type: type, x: x, y: y, z: z };

        this.scene.add(mesh);
        this.world.set(key, mesh);
    }

    removeBlock(x, y, z) {
        const key = `${x},${y},${z}`;
        const block = this.world.get(key);
        if (block) {
            this.scene.remove(block);
            this.world.delete(key);

            // 清理几何体和材质
            block.geometry.dispose();
            block.material.dispose();
        }
    }

    getBlock(x, y, z) {
        const key = `${x},${y},${z}`;
        return this.world.get(key);
    }

    generateTree(x, groundY, z) {
        const trunkHeight = 4 + Math.floor(Math.random() * 3);

        // 生成树干
        for (let y = 0; y < trunkHeight; y++) {
            this.addBlock(x, groundY + y, z, 'wood');
        }

        // 生成树叶
        const leavesY = groundY + trunkHeight;
        for (let dx = -2; dx <= 2; dx++) {
            for (let dz = -2; dz <= 2; dz++) {
                for (let dy = 0; dy < 3; dy++) {
                    if (dx === 0 && dz === 0 && dy === 0) continue;
                    if (Math.abs(dx) === 2 && Math.abs(dz) === 2) continue;
                    if (Math.random() < 0.8) {
                        this.addBlock(x + dx, leavesY + dy, z + dz, 'leaves');
                    }
                }
            }
        }
    }

    destroyBlock() {
        // 从相机中心发射射线
        this.raycaster.setFromCamera(new THREE.Vector2(0, 0), this.camera);
        const intersects = this.raycaster.intersectObjects(this.scene.children);

        if (intersects.length > 0) {
            const intersect = intersects[0];
            const block = intersect.object;

            // 检查距离是否在破坏范围内
            if (intersect.distance <= 5 && block.userData && block.userData.type) {
                this.removeBlock(block.userData.x, block.userData.y, block.userData.z);

                // 添加破坏效果
                this.createBreakEffect(block.position);
            }
        }
    }

    placeBlock() {
        // 从相机中心发射射线
        this.raycaster.setFromCamera(new THREE.Vector2(0, 0), this.camera);
        const intersects = this.raycaster.intersectObjects(this.scene.children);

        if (intersects.length > 0) {
            const intersect = intersects[0];

            // 检查距离是否在放置范围内
            if (intersect.distance <= 5) {
                const face = intersect.face;
                const block = intersect.object;

                // 计算新方块的位置
                const newPosition = new THREE.Vector3();
                newPosition.copy(intersect.point);
                newPosition.add(face.normal.multiplyScalar(0.5));
                newPosition.floor();

                // 检查位置是否被占用
                const existingBlock = this.getBlock(newPosition.x, newPosition.y, newPosition.z);
                if (!existingBlock) {
                    // 检查是否与玩家位置冲突
                    const playerPos = this.camera.position;
                    const playerBox = new THREE.Box3(
                        new THREE.Vector3(playerPos.x - 0.3, playerPos.y - 1.8, playerPos.z - 0.3),
                        new THREE.Vector3(playerPos.x + 0.3, playerPos.y + 0.1, playerPos.z + 0.3)
                    );

                    const blockBox = new THREE.Box3(
                        new THREE.Vector3(newPosition.x - 0.5, newPosition.y - 0.5, newPosition.z - 0.5),
                        new THREE.Vector3(newPosition.x + 0.5, newPosition.y + 0.5, newPosition.z + 0.5)
                    );

                    if (!playerBox.intersectsBox(blockBox)) {
                        this.addBlock(newPosition.x, newPosition.y, newPosition.z, this.selectedBlock);

                        // 添加放置效果
                        this.createPlaceEffect(newPosition);
                    }
                }
            }
        }
    }

    createBreakEffect(position) {
        // 创建简单的破坏粒子效果
        const particleCount = 10;
        const particles = new THREE.Group();

        for (let i = 0; i < particleCount; i++) {
            const geometry = new THREE.BoxGeometry(0.1, 0.1, 0.1);
            const material = new THREE.MeshBasicMaterial({
                color: Math.random() * 0xffffff,
                transparent: true,
                opacity: 0.8
            });
            const particle = new THREE.Mesh(geometry, material);

            particle.position.copy(position);
            particle.position.add(new THREE.Vector3(
                (Math.random() - 0.5) * 2,
                (Math.random() - 0.5) * 2,
                (Math.random() - 0.5) * 2
            ));

            particles.add(particle);
        }

        this.scene.add(particles);

        // 动画粒子
        const animate = () => {
            particles.children.forEach(particle => {
                particle.position.y -= 0.05;
                particle.material.opacity -= 0.02;
            });

            if (particles.children[0].material.opacity > 0) {
                requestAnimationFrame(animate);
            } else {
                this.scene.remove(particles);
                particles.children.forEach(particle => {
                    particle.geometry.dispose();
                    particle.material.dispose();
                });
            }
        };
        animate();
    }

    createPlaceEffect(position) {
        // 创建简单的放置效果
        const geometry = new THREE.BoxGeometry(1.1, 1.1, 1.1);
        const material = new THREE.MeshBasicMaterial({
            color: 0xffffff,
            transparent: true,
            opacity: 0.3,
            wireframe: true
        });
        const effect = new THREE.Mesh(geometry, material);
        effect.position.copy(position);

        this.scene.add(effect);

        // 动画效果
        const animate = () => {
            effect.material.opacity -= 0.05;
            effect.scale.multiplyScalar(1.02);

            if (effect.material.opacity > 0) {
                requestAnimationFrame(animate);
            } else {
                this.scene.remove(effect);
                effect.geometry.dispose();
                effect.material.dispose();
            }
        };
        animate();
    }
    
    animate() {
        requestAnimationFrame(() => this.animate());
        
        if (this.controls) {
            this.controls.update();
        }
        
        this.updateUI();
        this.renderer.render(this.scene, this.camera);
    }
    
    updateUI() {
        // 更新位置信息
        const pos = this.camera.position;
        document.getElementById('position').textContent =
            `${Math.floor(pos.x)}, ${Math.floor(pos.y)}, ${Math.floor(pos.z)}`;

        // 更新方块数量
        const blockCount = Array.from(this.world.values()).length;
        document.getElementById('blockCount').textContent = blockCount;

        // 更新FPS
        this.updateFPS();

        // 更新准星高亮
        this.updateCrosshairHighlight();
    }

    updateFPS() {
        if (!this.lastTime) this.lastTime = performance.now();
        if (!this.frameCount) this.frameCount = 0;

        this.frameCount++;
        const currentTime = performance.now();

        if (currentTime - this.lastTime >= 1000) {
            const fps = Math.round((this.frameCount * 1000) / (currentTime - this.lastTime));
            document.getElementById('fps').textContent = fps;
            this.frameCount = 0;
            this.lastTime = currentTime;
        }
    }

    updateCrosshairHighlight() {
        // 检测准星指向的方块
        this.raycaster.setFromCamera(new THREE.Vector2(0, 0), this.camera);
        const intersects = this.raycaster.intersectObjects(this.scene.children);

        // 移除之前的高亮
        if (this.highlightedBlock) {
            this.scene.remove(this.highlightedBlock);
            this.highlightedBlock.geometry.dispose();
            this.highlightedBlock.material.dispose();
            this.highlightedBlock = null;
        }

        if (intersects.length > 0 && intersects[0].distance <= 5) {
            const intersect = intersects[0];
            const block = intersect.object;

            if (block.userData && block.userData.type) {
                // 创建高亮边框
                const geometry = new THREE.BoxGeometry(1.01, 1.01, 1.01);
                const material = new THREE.MeshBasicMaterial({
                    color: 0xffffff,
                    transparent: true,
                    opacity: 0.3,
                    wireframe: true
                });

                this.highlightedBlock = new THREE.Mesh(geometry, material);
                this.highlightedBlock.position.copy(block.position);
                this.scene.add(this.highlightedBlock);
            }
        }
    }

    toggleFullscreen() {
        if (!document.fullscreenElement) {
            document.documentElement.requestFullscreen();
        } else {
            document.exitFullscreen();
        }
    }

    resetWorld() {
        if (confirm('确定要重置世界吗？这将删除所有方块！')) {
            // 清除所有方块
            this.world.forEach(block => {
                this.scene.remove(block);
                block.geometry.dispose();
                block.material.dispose();
            });
            this.world.clear();

            // 重新生成世界
            this.generateWorld();

            // 重置玩家位置
            this.camera.position.set(0, 32, 0);
        }
    }

    saveWorld() {
        try {
            const worldData = {
                blocks: [],
                playerPosition: {
                    x: this.camera.position.x,
                    y: this.camera.position.y,
                    z: this.camera.position.z
                },
                playerRotation: {
                    x: this.camera.rotation.x,
                    y: this.camera.rotation.y,
                    z: this.camera.rotation.z
                }
            };

            // 保存所有方块数据
            this.world.forEach((block, key) => {
                const [x, y, z] = key.split(',').map(Number);
                worldData.blocks.push({
                    x: x,
                    y: y,
                    z: z,
                    type: block.userData.type
                });
            });

            // 保存到本地存储
            localStorage.setItem('minecraftWorld', JSON.stringify(worldData));

            // 显示保存成功消息
            this.showMessage('世界已保存！');
        } catch (error) {
            console.error('保存世界失败:', error);
            this.showMessage('保存失败！');
        }
    }

    loadWorld() {
        try {
            const savedData = localStorage.getItem('minecraftWorld');
            if (!savedData) {
                this.showMessage('没有找到保存的世界！');
                return;
            }

            const worldData = JSON.parse(savedData);

            // 清除当前世界
            this.world.forEach(block => {
                this.scene.remove(block);
                block.geometry.dispose();
                block.material.dispose();
            });
            this.world.clear();

            // 加载方块
            worldData.blocks.forEach(blockData => {
                this.addBlock(blockData.x, blockData.y, blockData.z, blockData.type);
            });

            // 恢复玩家位置
            if (worldData.playerPosition) {
                this.camera.position.set(
                    worldData.playerPosition.x,
                    worldData.playerPosition.y,
                    worldData.playerPosition.z
                );
            }

            if (worldData.playerRotation) {
                this.camera.rotation.set(
                    worldData.playerRotation.x,
                    worldData.playerRotation.y,
                    worldData.playerRotation.z
                );
            }

            this.showMessage('世界已加载！');
        } catch (error) {
            console.error('加载世界失败:', error);
            this.showMessage('加载失败！');
        }
    }

    showMessage(text) {
        // 创建消息元素
        const message = document.createElement('div');
        message.textContent = text;
        message.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 20px;
            border-radius: 5px;
            font-size: 18px;
            z-index: 1000;
            pointer-events: none;
        `;

        document.body.appendChild(message);

        // 3秒后移除消息
        setTimeout(() => {
            document.body.removeChild(message);
        }, 3000);
    }
}

// 第一人称控制器类
class FirstPersonControls {
    constructor(camera, domElement) {
        this.camera = camera;
        this.domElement = domElement;
        this.enabled = false;
        
        this.moveSpeed = 10;
        this.jumpSpeed = 15;
        this.gravity = -30;
        this.velocity = new THREE.Vector3();
        this.direction = new THREE.Vector3();
        
        this.keys = {
            forward: false,
            backward: false,
            left: false,
            right: false,
            jump: false
        };
        
        this.setupEventListeners();
    }
    
    setupEventListeners() {
        document.addEventListener('keydown', (event) => this.onKeyDown(event));
        document.addEventListener('keyup', (event) => this.onKeyUp(event));
        document.addEventListener('mousemove', (event) => this.onMouseMove(event));
    }
    
    onKeyDown(event) {
        switch(event.code) {
            case 'KeyW': this.keys.forward = true; break;
            case 'KeyS': this.keys.backward = true; break;
            case 'KeyA': this.keys.left = true; break;
            case 'KeyD': this.keys.right = true; break;
            case 'Space': this.keys.jump = true; break;
        }
    }
    
    onKeyUp(event) {
        switch(event.code) {
            case 'KeyW': this.keys.forward = false; break;
            case 'KeyS': this.keys.backward = false; break;
            case 'KeyA': this.keys.left = false; break;
            case 'KeyD': this.keys.right = false; break;
            case 'Space': this.keys.jump = false; break;
        }
    }
    
    onMouseMove(event) {
        if (!this.enabled) return;
        
        const sensitivity = 0.002;
        this.camera.rotation.y -= event.movementX * sensitivity;
        this.camera.rotation.x -= event.movementY * sensitivity;
        this.camera.rotation.x = Math.max(-Math.PI/2, Math.min(Math.PI/2, this.camera.rotation.x));
    }
    
    update() {
        if (!this.enabled) return;

        const delta = 0.016; // 假设60fps

        // 重置方向
        this.direction.set(0, 0, 0);

        // 计算移动方向
        if (this.keys.forward) this.direction.z -= 1;
        if (this.keys.backward) this.direction.z += 1;
        if (this.keys.left) this.direction.x -= 1;
        if (this.keys.right) this.direction.x += 1;

        // 标准化方向向量
        if (this.direction.length() > 0) {
            this.direction.normalize();

            // 应用相机旋转
            const quaternion = new THREE.Quaternion();
            quaternion.setFromEuler(new THREE.Euler(0, this.camera.rotation.y, 0));
            this.direction.applyQuaternion(quaternion);

            // 更新水平速度
            this.velocity.x = this.direction.x * this.moveSpeed;
            this.velocity.z = this.direction.z * this.moveSpeed;
        } else {
            // 如果没有输入，逐渐减速
            this.velocity.x *= 0.8;
            this.velocity.z *= 0.8;
        }

        // 跳跃逻辑
        const groundLevel = this.getGroundLevel(this.camera.position.x, this.camera.position.z);
        const isOnGround = this.camera.position.y <= groundLevel + 1.8;

        if (this.keys.jump && isOnGround) {
            this.velocity.y = this.jumpSpeed;
        }

        // 应用重力
        this.velocity.y += this.gravity * delta;

        // 更新位置
        const newPosition = this.camera.position.clone();
        newPosition.add(this.velocity.clone().multiplyScalar(delta));

        // 碰撞检测
        if (this.checkCollision(newPosition)) {
            // 如果有碰撞，只更新Y轴
            this.camera.position.y = Math.max(newPosition.y, groundLevel + 1.8);
            if (this.camera.position.y === groundLevel + 1.8) {
                this.velocity.y = 0;
            }
        } else {
            this.camera.position.copy(newPosition);
            // 地面碰撞检测
            if (this.camera.position.y < groundLevel + 1.8) {
                this.camera.position.y = groundLevel + 1.8;
                this.velocity.y = 0;
            }
        }
    }

    getGroundLevel(x, z) {
        // 简单的地面高度检测
        const floorX = Math.floor(x);
        const floorZ = Math.floor(z);

        for (let y = 50; y >= 0; y--) {
            if (game && game.getBlock(floorX, y, floorZ)) {
                return y;
            }
        }
        return 0;
    }

    checkCollision(position) {
        // 简单的碰撞检测
        const playerRadius = 0.3;
        const playerHeight = 1.8;

        // 检查玩家周围的方块
        for (let dx = -1; dx <= 1; dx++) {
            for (let dz = -1; dz <= 1; dz++) {
                for (let dy = 0; dy < 2; dy++) {
                    const blockX = Math.floor(position.x + dx * playerRadius);
                    const blockY = Math.floor(position.y + dy);
                    const blockZ = Math.floor(position.z + dz * playerRadius);

                    if (game && game.getBlock(blockX, blockY, blockZ)) {
                        return true;
                    }
                }
            }
        }
        return false;
    }
}

// 全局游戏变量
let game = null;

// 启动游戏
window.addEventListener('load', () => {
    console.log('页面加载完成，开始初始化游戏...');

    // 检查Three.js是否加载
    if (typeof THREE === 'undefined') {
        console.error('Three.js 库未加载！');
        document.getElementById('loading').textContent = '错误：Three.js 库加载失败！';
        return;
    }

    console.log('Three.js 库已加载，版本:', THREE.REVISION);

    try {
        game = new MinecraftGame();
        console.log('游戏初始化成功！');
    } catch (error) {
        console.error('游戏初始化失败:', error);
        document.getElementById('loading').textContent = '游戏初始化失败：' + error.message;
    }
});
