<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>我的世界 - Minecraft Clone</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Courier New', monospace;
            background: #87CEEB;
            overflow: hidden;
            cursor: none;
        }

        #gameContainer {
            position: relative;
            width: 100vw;
            height: 100vh;
        }

        #canvas {
            display: block;
            width: 100%;
            height: 100%;
        }

        #ui {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 100;
        }

        #crosshair {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 20px;
            height: 20px;
            pointer-events: none;
        }

        #crosshair::before,
        #crosshair::after {
            content: '';
            position: absolute;
            background: white;
            border: 1px solid black;
        }

        #crosshair::before {
            top: 50%;
            left: 0;
            width: 100%;
            height: 2px;
            transform: translateY(-50%);
        }

        #crosshair::after {
            left: 50%;
            top: 0;
            width: 2px;
            height: 100%;
            transform: translateX(-50%);
        }

        #inventory {
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 5px;
            pointer-events: auto;
        }

        .inventory-slot {
            width: 50px;
            height: 50px;
            background: rgba(0, 0, 0, 0.7);
            border: 2px solid #666;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            position: relative;
        }

        .inventory-slot.active {
            border-color: white;
            background: rgba(255, 255, 255, 0.2);
        }

        .inventory-slot img {
            width: 32px;
            height: 32px;
            image-rendering: pixelated;
        }

        .inventory-slot .count {
            position: absolute;
            bottom: 2px;
            right: 2px;
            color: white;
            font-size: 12px;
            text-shadow: 1px 1px 0 black;
        }

        #info {
            position: absolute;
            top: 10px;
            left: 10px;
            color: white;
            text-shadow: 1px 1px 0 black;
            font-size: 14px;
            pointer-events: none;
        }

        #instructions {
            position: absolute;
            top: 10px;
            right: 10px;
            color: white;
            text-shadow: 1px 1px 0 black;
            font-size: 12px;
            text-align: right;
            pointer-events: none;
        }

        #loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 24px;
            text-shadow: 2px 2px 0 black;
            z-index: 200;
        }

        .hidden {
            display: none !important;
        }

        /* 方块纹理样式 */
        .block-grass { background: #7CB342; }
        .block-dirt { background: #8D6E63; }
        .block-stone { background: #757575; }
        .block-wood { background: #8D6E63; }
        .block-leaves { background: #4CAF50; }
        .block-sand { background: #FDD835; }
        .block-water { background: #2196F3; }
        .block-cobblestone { background: #616161; }
    </style>
</head>
<body>
    <div id="gameContainer">
        <canvas id="canvas"></canvas>
        
        <div id="ui">
            <div id="crosshair"></div>
            
            <div id="info">
                <div>坐标: <span id="position">0, 0, 0</span></div>
                <div>FPS: <span id="fps">60</span></div>
                <div>方块: <span id="blockCount">0</span></div>
            </div>
            
            <div id="instructions">
                <div>WASD - 移动</div>
                <div>空格 - 跳跃</div>
                <div>鼠标 - 视角</div>
                <div>左键 - 破坏方块</div>
                <div>右键 - 放置方块</div>
                <div>1-8 - 选择方块</div>
                <div>ESC - 释放鼠标</div>
                <div>F - 全屏切换</div>
                <div>R - 重置世界</div>
                <div>P - 保存世界</div>
                <div>L - 加载世界</div>
            </div>
            
            <div id="inventory">
                <div class="inventory-slot active" data-block="grass">
                    <div class="block-grass" style="width: 32px; height: 32px;"></div>
                </div>
                <div class="inventory-slot" data-block="dirt">
                    <div class="block-dirt" style="width: 32px; height: 32px;"></div>
                </div>
                <div class="inventory-slot" data-block="stone">
                    <div class="block-stone" style="width: 32px; height: 32px;"></div>
                </div>
                <div class="inventory-slot" data-block="wood">
                    <div class="block-wood" style="width: 32px; height: 32px;"></div>
                </div>
                <div class="inventory-slot" data-block="leaves">
                    <div class="block-leaves" style="width: 32px; height: 32px;"></div>
                </div>
                <div class="inventory-slot" data-block="sand">
                    <div class="block-sand" style="width: 32px; height: 32px;"></div>
                </div>
                <div class="inventory-slot" data-block="cobblestone">
                    <div class="block-cobblestone" style="width: 32px; height: 32px;"></div>
                </div>
                <div class="inventory-slot" data-block="water">
                    <div class="block-water" style="width: 32px; height: 32px;"></div>
                </div>
            </div>
        </div>
        
        <div id="loading">
            正在加载游戏...
        </div>
    </div>

    <!-- Three.js 库 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    
    <!-- 游戏脚本 -->
    <script src="game.js"></script>
</body>
</html>
